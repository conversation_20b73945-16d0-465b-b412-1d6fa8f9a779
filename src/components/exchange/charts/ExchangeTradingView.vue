<template>
  <div class="exchange-tradingview-wrap">
    <div id="kline_container" ref="kline_container" class="kline_container"></div>
    <div
        v-if="Loading"
        v-loading="Loading"
        class="loadingBox"
      >
    </div>
  </div>
</template>
<script lang="ts" setup>
import { setStorage, getStorage } from '~/utils'
import jstz from 'jstz'
import MonoLoading from '~/components/common/icon-svg/MonoLoading.vue'
import useTradingView from '~/composables/useTradingView'
import useDatafeedAction from '~/composables/useDatafeedAction'
import * as widget1 from '~/public/tradingview/charting_library/charting_library'
console.log(333, widget1)
import { commonStore } from '~/stores/commonStore'
const store = commonStore()
const { pairInfo, isPairDetail, pair } = storeToRefs(store)
const custom_css_url = '/tradingview/tradingview_style/tradingview_custom.css'
const colorMode = useColorMode()
const { locale, t } = useI18n()
const klineNotLoad = ref(false)
const { option, tradingviewLangMap } = useTradingView(colorMode.preference, 'green-up')
const tradingViewOption = option[colorMode.preference]
const props = defineProps({
  pair: {
    type: String,
    default: ''
  },
  resolution: {
    type: String,
    default: '15m'
  },
  isShowTechnicalIndicator: {
    type: Boolean,
    default: false
  },
  isShowTradingViewSetting: {
    type: Boolean,
    default: false
  },
  isLoading: {
    type: Boolean,
    default: true
  }
})
const resolutionReMap: any = {
  'line': 1,
  '1m': 1,
  '5m': 5,
  '15m': 15,
  '30m': 30,
  '1h': 60,
  '2h': 120,
  '4h': 240,
  '6h': 360,
  '8h': 480,
  '12h': 720,
  '1d': '1d',
  '1w': '1w',
  '1M': '1M'
}
const emit = defineEmits(['closeTechnicalIndicator','closeTradingViewSetting'])
const widgetOption = {
  debug: false,
  symbol: props.pair || pair.value,
  timezone: 'Asia/Shanghai',
  container: 'kline_container',
  library_path: '/tradingview/charting_library/',
  custom_css_url,
  auto_save_delay: 0.001,
  datafeed: useDatafeedAction(pairInfo.value),
  interval: props.resolution ? resolutionReMap[props.resolution] : '15',
  locale: tradingviewLangMap[locale.value] || locale.value,
  autosize: true,
  disabled_features: [
    "header_screenshot",
    "header_symbol_search",
    "header_undo_redo",
    "header_compare",
    "header_chart_type",
    "header_resolutions",
    "header_widget",
    "volume_force_overlay",
    "use_localstorage_for_settings",
    "symbol_search_hot_key",
    'timeframes_toolbar'
  ],
  enabled_features: [
    "keep_left_toolbar_visible_on_small_screens",
    "save_chart_properties_to_local_storage"
  ],
  toolbar_bg: 'transparent',
  ...tradingViewOption
} as any
let widget: any = null
let datafeedInstance: any = null
const Loading = ref(true)
const initChart = async () => {
  const currentPair = props.pair || pair.value

  if (!currentPair) {
    return
  }

  if (props.resolution === '1M') {
    await new Promise(resolve => setTimeout(resolve, 200))
  }

  datafeedInstance = useDatafeedAction(pairInfo.value)
  widgetOption.datafeed = datafeedInstance
  widgetOption.symbol = currentPair
  widgetOption.interval = props.resolution ? resolutionReMap[props.resolution] : '15',
  widget = new window.TradingView.widget(widgetOption)
  console.log(widgetOption, pairInfo.value, 'dhdudheudheuueuhedhdue')
  widget.onChartReady(() => {
    widget.activeChart().setChartType(1)
    widget.addCustomCSSFile(`/tradingview/tradingview_style/${colorMode.preference}.css`)
    Loading.value = false
  })
}

const previousResolution = ref(props.resolution)

watch(() => props.resolution, (newVal, oldVal) => {
  if (newVal && widget) {
    const isFromMonthly = oldVal === '1M' || previousResolution.value === '1M'
    const isToMonthly = newVal === '1M'
    const isToNonMonthly = newVal !== '1M'

    if ((isFromMonthly && isToNonMonthly) || (!isFromMonthly && isToMonthly)) {
      if (datafeedInstance && datafeedInstance.clearCache) {
        datafeedInstance.clearCache()
      }
      if (datafeedInstance && datafeedInstance.setForceRefresh) {
        datafeedInstance.setForceRefresh(true)
      }

      if (widget) {
        widget.remove()
        widget = null
      }

      setTimeout(() => {
        Loading.value = true
        datafeedInstance = useDatafeedAction(pairInfo.value)
        widgetOption.datafeed = datafeedInstance
        widgetOption.symbol = props.pair || pair.value
        widgetOption.interval = resolutionReMap[newVal]
        widget = new window.TradingView.widget(widgetOption)

        widget.onChartReady(() => {
          widget.activeChart().setChartType(1)
          widget.addCustomCSSFile(`/tradingview/tradingview_style/${colorMode.preference}.css`)
          Loading.value = false
        })
      }, 150)
    } else {
      widget.activeChart().setResolution(resolutionReMap[newVal])
    }
  }

  previousResolution.value = oldVal
})
const currentPairSymbol = computed(() => props.pair || pair.value)
const hasChanged = ref(false)

// 添加防抖机制避免快速切换
let symbolChangeTimer: any = null

watch(() => currentPairSymbol.value, (newVal, oldVal) => {
  console.log('🔄 TradingView币种切换检测:', {
    oldVal,
    newVal,
    'widget存在': !!widget,
    'props.isLoading': props.isLoading,
    'Loading': Loading.value
  })

  if (newVal !== oldVal && newVal !== undefined && widget) {
    // 清除之前的定时器
    if (symbolChangeTimer) {
      clearTimeout(symbolChangeTimer)
    }

    // 移除isLoading条件限制，专业版K线应该有独立的状态管理
    // 添加短暂延迟确保widget状态稳定
    symbolChangeTimer = setTimeout(() => {
      try {
        const interval = props.resolution ? resolutionReMap[props.resolution] : '15'
        console.log('🚀 执行TradingView setSymbol:', {
          symbol: newVal,
          interval: interval,
          resolution: props.resolution
        })
        widget.setSymbol(newVal, interval)
      } catch (error) {
        console.error('❌ TradingView setSymbol执行失败:', error)
      }
    }, 100)
  }
})

onMounted(() => {
  const currentPair = props.pair || pair.value

  if (currentPair && JSON.stringify(pairInfo.value) !== '{}') {
    initChart()
    hasChanged.value = true
  }
})

watch(() => pairInfo.value, (newPairInfo, oldPairInfo) => {
  const currentPair = props.pair || pair.value

  if (!hasChanged.value && currentPair && JSON.stringify(newPairInfo || {}) !== '{}') {
    hasChanged.value = true
    initChart()
  }
}, { immediate: true })

watch(() => colorMode.preference, (val) => {
  widget.changeTheme(colorMode.preference === 'light' ? 'Light' : 'Dark')
  setTimeout(() => {
    const options = useTradingView(val, 'green-up').option[val]
    widget.applyOverrides(options.overrides)
    widget.applyStudiesOverrides(options.studies_overrides)
  }, 10)
  widget.addCustomCSSFile(`/tradingview/tradingview_style/${val}.css`)
})
watch(() => props.isShowTechnicalIndicator, (TechnicalIndicator) => {
  if (TechnicalIndicator && widget) {
    widget.chart().executeActionById("insertIndicator")
    setTimeout(() => {
      emit('closeTechnicalIndicator')
    }, 100)
  }
}, { immediate: true })

watch(() => props.isShowTradingViewSetting, (Setting) => {
  if (Setting && widget) {
    widget.chart().executeActionById("chartProperties")
    setTimeout(() => {
      emit('closeTradingViewSetting')
    }, 100)
  }
}, { immediate: true })

onUnmounted(() => {
  // 清理定时器
  if (symbolChangeTimer) {
    clearTimeout(symbolChangeTimer)
    symbolChangeTimer = null
  }

  // 清理widget
  if (widget) {
    try {
      widget.remove()
      widget = null
    } catch (error) {
      console.error('清理TradingView widget失败:', error)
    }
  }
})
</script>
<style lang="scss" scoped>  
.exchange-tradingview-wrap{
  width:100%;
  height:calc(100% - 46px);
  position:relative;
  .loadingBox {
    width: 100%;
    height: 100%;
    position: absolute;
    @include bg-color(bg-primary);
    z-index: 999;
    left: 0;
    top: 0;
  }
  .kline_container{
    background: transparent;
    width: 100%;
    height: calc(100%);
  }
}
@include mb{
  .exchange-tradingview-wrap{
    height:calc(100% - 44px);
  }
}
</style>