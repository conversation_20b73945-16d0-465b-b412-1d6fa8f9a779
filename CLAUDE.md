# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 常用命令

### 开发环境
- `npm run dev` - 启动开发服务器（本地环境）
- `npm run test` - 启动测试环境开发服务器
- `npm run pro` - 启动生产环境开发服务器

### 构建和部署
- `npm run build` - 生产环境构建（需要 4GB 内存）
- `npm run build:test` - 测试环境构建
- `npm run build:local` - 本地环境构建
- `npm run static` - 上传静态资源到 OSS
- `npm run preview` - 预览生产构建

### 其他命令
- `npm run generate` - 生成静态站点
- `npm run start` - 启动生产服务器

## 项目架构

### 技术栈
- **框架**: Nuxt 3 + Vue 3 + TypeScript
- **UI 组件**: Element Plus + Vant（移动端）
- **状态管理**: Pinia
- **样式**: SCSS
- **图表**: ECharts + TradingView + KlineCharts
- **国际化**: Vue I18n
- **构建工具**: Vite

### 目录结构
```
src/
├── api/          # API 接口层
├── assets/       # 静态资源（图片、样式）
├── components/   # Vue 组件
│   ├── common/   # 通用组件
│   ├── exchange/ # 现货交易相关组件
│   ├── future/   # 期货杠杆相关组件
│   ├── header/   # 头部组件
│   └── ...
├── composables/  # Vue 组合式函数
├── config/       # 配置文件
├── layouts/      # 布局组件
├── locales/      # 国际化配置
├── pages/        # 页面组件（路由）
├── plugins/      # 插件
├── stores/       # Pinia 状态管理
└── utils/        # 工具函数
```

### 核心功能模块
1. **Exchange（现货交易）**: 包含 K 线图表、订单簿、交易表单等
2. **Future（期货杠杆）**: 合约交易、杠杆调节、保证金管理等
3. **User Center（用户中心）**: 资产管理、账户安全、API 管理等
4. **Markets（行情）**: 币种列表、排行榜、搜索等

### 状态管理 (Pinia)
- `commonStore`: 公共数据（币种列表、汇率、交易对信息等）
- `futureStore`: 期货相关状态
- `marketsStore`: 行情数据
- `useUserStore`: 用户信息和账户状态

### 核心 Composables
- `useDatafeedAction`: TradingView 数据源处理
- `useTradingView`: TradingView 图表集成
- `useOriginal`: 原生 K 线图表
- `useCookieUniversal`: 统一 Cookie 处理

### 主要配置
- **环境配置**: 通过 `.env.local`、`.env.development`、`.env.production` 管理
- **API 代理**: 开发环境代理到 `https://api.ktx.one`
- **多语言**: 支持中文（简/繁）、英语、日语、韩语
- **主题**: 支持明/暗主题切换
- **构建优化**: 
  - 手动分包（TradingView、Element UI、Vue 核心等）
  - Terser 压缩
  - Tree-shaking

### WebSocket 连接
- 行情数据通过 WebSocket 实时推送
- 主要连接地址: `wss://stream-market.tonetou.com`
- 支持数据压缩 (SOCKET_ISZIP)

### 图表集成
- **TradingView**: 专业金融图表库
- **KlineCharts**: 轻量级 K 线图表
- **ECharts**: 深度图等其他图表

### 移动端适配
- 使用 Vant 组件库
- 响应式设计
- 支持 PWA

### 国际化 (i18n)
- 基于 Vue I18n 实现
- 支持路由前缀策略
- 自动检测浏览器语言
- Cookie 持久化语言设置

### 安全和认证
- Google 验证码集成
- KYC 身份验证
- API 密钥管理
- 子账户系统

### 部署相关
- 使用 PM2 进行进程管理
- 支持 Docker 部署
- 静态资源 CDN 分发
- OSS 对象存储集成